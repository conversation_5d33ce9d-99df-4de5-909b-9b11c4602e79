<script lang="ts" setup>
import type { UploadFile } from 'ant-design-vue';

import type { CloneSetConfig } from '#/api/core/digital-human';
import type { TallySetConfig } from '#/api/core/voice-clone';

import { computed, h, onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';

import { Page } from '@vben/common-ui';

import {
  Button,
  Card,
  Checkbox,
  Input,
  message,
  Modal,
  Radio,
  Upload,
} from 'ant-design-vue';

import { getCloneSetConfig } from '#/api/core/digital-human';
import {
  getTallySetConfig,
  voiceTraining,
  voiceTtsUpload,
} from '#/api/core/voice-clone';

// 定义组件名称
defineOptions({ name: 'AIVoiceClone' });

// 路由实例
const router = useRouter();

// 响应式数据
const loading = ref(false);
const uploading = ref(false);
const uploadError = ref(false);
const fileList = ref<UploadFile[]>([]);

// 表单数据
const formData = ref({
  cloneMode: 1, // 1: 入门体验版, 2: 高保真音色
  cloneTitle: '', // 克隆标题
  audioFile: null as null | UploadFile,
  agreeToTerms: false,
});

// 配置数据
const cloneSetConfig = ref<CloneSetConfig | null>(null);
const tallySetConfig = ref<null | TallySetConfig>(null);
const configLoading = ref(false);

// 克隆模式选项
const cloneModeOptions = [
  { label: '入门体验版克隆', value: 1 },
  { label: '高保真音色克隆', value: 2 },
];

// 计算属性
// 支持的文件格式
const supportedFormats = computed(() => {
  return formData.value.cloneMode === 1 ? 'mp3' : 'wav、mp3、m4a';
});

// 文件上传的 accept 属性
const acceptTypes = computed(() => {
  return formData.value.cloneMode === 1 ? '.mp3' : '.wav,.mp3,.m4a';
});

// 上传要求文本
const uploadRequirements = computed(() => {
  return formData.value.cloneMode === 1
    ? [
        '禁止环境噪音、全程一人说话、保持语速均匀、声音大小一致',
        '支持在线录音或上传音频，音频要mp3(小写)格式',
        '上传的音频长度15秒以上，容量3M以内',
        '录制过程中要保证环境安静不得有明显噪音',
        '声音不要进行混响和任何特效处理要保持自然',
        '录制过程中只能有一个人的声音禁止多人人声',
        '录制时要语速均衡，声音大小不要忽大忽小',
      ]
    : [
        '音频文件上传支持：wav、mp3、m4a格式',
        '上传的音频要求30秒以上,容量10M以内',
        '录制过程确保环境安静没有明显的环境噪音',
        '录制过程中不要长时间不说话要保证语速平稳',
        '录制过程中不要声音语调时高时低保持音量均衡',
        '录制过程中只能有一个人的声音避免其它人声',
        '声音不得进行混响特效处理，更不得带背景音乐和音效',
        '要保证为原音录制不得对音频进行切分与合并以及剪辑',
      ];
});

// 消耗点数
const consumePoints = computed(() => {
  if (!tallySetConfig.value) return '0';
  return formData.value.cloneMode === 1
    ? tallySetConfig.value.voice_deduct
    : tallySetConfig.value.voice_high_deduct;
});

// 表单验证
const canSubmit = computed(() => {
  return (
    formData.value.cloneTitle.trim() && // 标题必填
    formData.value.audioFile &&
    formData.value.audioFile.url && // 确保文件已上传并有URL
    formData.value.agreeToTerms &&
    !uploading.value &&
    !loading.value
  );
});

// 方法
// 初始化配置
const initConfigs = async () => {
  try {
    configLoading.value = true;

    // 并行获取配置
    const [cloneConfig, tallyConfig] = await Promise.all([
      getCloneSetConfig(),
      getTallySetConfig(),
    ]);

    cloneSetConfig.value = cloneConfig;
    tallySetConfig.value = tallyConfig;

    console.warn('配置初始化成功:', { cloneConfig, tallyConfig });
  } catch (error) {
    console.error('配置初始化失败:', error);
    message.error('配置加载失败，请刷新页面重试');
  } finally {
    configLoading.value = false;
  }
};

// 音频上传前验证
const beforeAudioUpload = (file: File) => {
  // 检查文件类型
  const isValidType =
    formData.value.cloneMode === 1
      ? file.type === 'audio/mp3' || file.type === 'audio/mpeg'
      : ['audio/m4a', 'audio/mp3', 'audio/mpeg', 'audio/wav'].includes(
          file.type,
        );

  if (!isValidType) {
    const formats = formData.value.cloneMode === 1 ? 'MP3' : 'WAV、MP3、M4A';
    message.error(`只能上传 ${formats} 格式的音频文件！`);
    return false;
  }

  // 检查文件大小
  const maxSize = formData.value.cloneMode === 1 ? 3 : 10; // MB
  const isValidSize = file.size / 1024 / 1024 < maxSize;
  if (!isValidSize) {
    message.error(`音频文件大小不能超过 ${maxSize}MB！`);
    return false;
  }

  return true;
};

// 上传状态变化处理
const handleUploadChange = (info: {
  file: UploadFile;
  fileList: UploadFile[];
}) => {
  const { file, fileList: newFileList } = info;

  console.warn('上传状态变化:', file.status, file);

  // 更新文件列表
  fileList.value = newFileList;

  switch (file.status) {
    case 'done': {
      uploading.value = false;

      console.warn('上传接口响应:', file.response);

      // 上传成功，更新文件信息
      let audioUrl = '';
      if (file.response) {
        // 根据后端响应格式提取URL
        if (typeof file.response === 'string') {
          audioUrl = file.response;
        } else if (file.response.data) {
          audioUrl = file.response.data;
        } else if (file.response.url) {
          audioUrl = file.response.url;
        }
      }

      if (audioUrl) {
        const fileName = audioUrl.slice(
          Math.max(0, audioUrl.lastIndexOf('/') + 1),
        );

        formData.value.audioFile = {
          ...file,
          name: fileName,
          url: audioUrl,
          status: 'done',
        } as UploadFile;

        console.warn('音频上传成功，文件URL:', audioUrl);
        message.success('音频上传成功');
      } else {
        // 响应格式不正确
        handleUploadError(file, '响应格式不正确');
      }

      break;
    }
    case 'error': {
      handleUploadError(file, file.error?.message || '上传失败');

      break;
    }
    case 'uploading': {
      uploading.value = true;
      uploadError.value = false;

      // 更新表单数据
      formData.value.audioFile = {
        ...file,
        status: 'uploading',
      } as UploadFile;

      console.warn('开始上传音频文件:', file.name);

      break;
    }
    // No default
  }
};

// 上传错误处理
const handleUploadError = (file: UploadFile, errorMessage: string) => {
  uploading.value = false;
  uploadError.value = true;

  console.error('音频上传失败:', errorMessage);
  message.error(`音频上传失败: ${errorMessage}`);

  // 保留文件信息但标记为错误状态
  formData.value.audioFile = {
    ...file,
    status: 'error',
  } as UploadFile;
};

// 移除音频文件
const handleAudioRemove = () => {
  formData.value.audioFile = null;
  fileList.value = [];
  uploadError.value = false;
};

// 查看用户协议
const viewUserAgreement = () => {
  if (cloneSetConfig.value?.protocol) {
    // 显示协议内容弹窗
    Modal.info({
      title: cloneSetConfig.value.protocol_name || '用户协议',
      content: h('div', {
        innerHTML: cloneSetConfig.value.protocol,
      }),
      width: 600,
      okText: '我知道了',
    });
  } else {
    message.warning('协议内容加载中，请稍后重试');
  }
};

// 提交表单
const handleSubmit = async () => {
  if (!canSubmit.value) return;

  try {
    loading.value = true;

    console.warn('开始提交音色克隆任务:', {
      cloneMode: formData.value.cloneMode,
      cloneTitle: formData.value.cloneTitle,
      audioFile: formData.value.audioFile,
      agreeToTerms: formData.value.agreeToTerms,
    });

    // 准备接口参数
    const params = {
      uid: 7, // 固定用户ID
      name: formData.value.cloneTitle.trim(), // 克隆标题
      voice_urls: [formData.value.audioFile?.url || ''], // 音频文件URL数组
    };

    // 根据克隆模式调用不同的接口
    let result;
    formData.value.cloneMode === 1
      ? await voiceTraining(params)
      : await voiceTtsUpload(params);

    console.warn('音色克隆接口响应:', result);

    message.success('音色克隆任务已提交！');

    // 跳转到数字人页面
    await router.push('/public-domain/ai-digital-human');

    // 重置表单
    formData.value = {
      cloneMode: 1,
      cloneTitle: '',
      audioFile: null,
      agreeToTerms: false,
    };
  } catch (error) {
    console.error('提交音色克隆任务失败:', error);
    const errorMessage =
      error instanceof Error ? error.message : '提交失败，请重试';
    message.error(`提交失败: ${errorMessage}`);
  } finally {
    loading.value = false;
  }
};

// 返回到AI数字人页面
const handleGoBack = () => {
  router.push('/public-domain/ai-digital-human');
};

// 组件挂载时初始化
onMounted(() => {
  initConfigs();
});
</script>

<template>
  <Page auto-content-height>
    <div class="ai-voice-clone-container">
      <Card class="form-card" :loading="configLoading">
        <template #title>
          <Button type="text" class="back-button" @click="handleGoBack">
            <template #icon>
              <svg
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                class="back-icon"
              >
                <path
                  d="M19 12H5M12 19L5 12L12 5"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </template>
            返回
          </Button>
        </template>

        <div class="form-content">
          <!-- 克隆模式选择 -->
          <div class="form-section">
            <div class="section-title">克隆模式选择</div>
            <Radio.Group
              v-model:value="formData.cloneMode"
              :options="cloneModeOptions"
              class="clone-mode-group"
            />
          </div>

          <!-- 克隆标题 -->
          <div class="form-section">
            <div class="section-title">克隆标题</div>
            <Input
              v-model:value="formData.cloneTitle"
              placeholder="请输入音色克隆的标题"
              :maxlength="50"
              show-count
              class="clone-title-input"
            />
          </div>

          <!-- 音频上传组件 -->
          <div class="form-section">
            <div class="section-title">
              音频上传
              <span class="format-hint">
                （支持格式：{{ supportedFormats }}）
              </span>
            </div>

            <Upload.Dragger
              :accept="acceptTypes"
              :before-upload="beforeAudioUpload"
              :file-list="fileList"
              :show-upload-list="false"
              action="https://szr.jiajs.cn/mobile/index/upload"
              name="file"
              class="audio-uploader"
              @change="handleUploadChange"
            >
              <div
                v-if="!formData.audioFile && !uploading"
                class="upload-placeholder"
              >
                <div class="upload-icon">
                  <svg
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M3 9V15H7L12 20V4L7 9H3ZM16.5 12C16.5 10.23 15.48 8.71 14 7.97V16.02C15.48 15.29 16.5 13.77 16.5 12ZM14 3.23V5.29C16.89 6.15 19 8.83 19 12C19 15.17 16.89 17.85 14 18.71V20.77C18.01 19.86 21 16.28 21 12C21 7.72 18.01 4.14 14 3.23Z"
                      fill="currentColor"
                    />
                  </svg>
                </div>
                <div class="upload-text">
                  <div class="upload-title">点击或拖拽音频文件到此区域上传</div>
                  <div class="upload-subtitle">
                    支持 {{ supportedFormats }} 格式
                  </div>
                </div>
              </div>

              <div v-else-if="uploading" class="upload-progress">
                <div class="progress-icon">
                  <svg
                    class="loading-icon"
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M12 2V6M12 18V22M4.93 4.93L7.76 7.76M16.24 16.24L19.07 19.07M2 12H6M18 12H22M4.93 19.07L7.76 16.24M16.24 7.76L19.07 4.93"
                      stroke="currentColor"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </svg>
                </div>
                <div class="progress-text">
                  <div class="progress-title">正在处理...</div>
                  <div class="progress-subtitle">请稍候</div>
                </div>
              </div>

              <div
                v-else-if="formData.audioFile?.status === 'done'"
                class="upload-success"
              >
                <div class="success-icon">
                  <svg
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M3 9V15H7L12 20V4L7 9H3ZM16.5 12C16.5 10.23 15.48 8.71 14 7.97V16.02C15.48 15.29 16.5 13.77 16.5 12ZM14 3.23V5.29C16.89 6.15 19 8.83 19 12C19 15.17 16.89 17.85 14 18.71V20.77C18.01 19.86 21 16.28 21 12C21 7.72 18.01 4.14 14 3.23Z"
                      fill="currentColor"
                    />
                  </svg>
                </div>
                <div class="success-text">
                  <div class="success-title">
                    {{ formData.audioFile?.name || '音频文件' }}
                  </div>
                  <div class="success-subtitle">
                    {{
                      Math.round(
                        ((formData.audioFile?.size || 0) / 1024 / 1024) * 100,
                      ) / 100
                    }}MB
                    <span v-if="formData.audioFile?.url" class="upload-status"
                      >已上传</span
                    >
                  </div>
                </div>
                <Button
                  type="text"
                  danger
                  size="small"
                  class="remove-btn"
                  @click.stop="handleAudioRemove"
                >
                  移除
                </Button>
              </div>

              <div
                v-else-if="formData.audioFile?.status === 'error'"
                class="upload-error"
              >
                <div class="error-icon">
                  <svg
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z"
                      fill="currentColor"
                    />
                  </svg>
                </div>
                <div class="error-text">
                  <div class="error-title">
                    {{ formData.audioFile?.name || '音频文件' }}
                  </div>
                  <div class="error-subtitle">上传失败，请重试</div>
                </div>
                <Button
                  type="text"
                  danger
                  size="small"
                  class="remove-btn"
                  @click.stop="handleAudioRemove"
                >
                  移除
                </Button>
              </div>
            </Upload.Dragger>
          </div>

          <!-- 上传要求说明 -->
          <div class="form-section">
            <div class="section-title">上传要求</div>
            <div class="requirements-list">
              <div
                v-for="(requirement, index) in uploadRequirements"
                :key="index"
                class="requirement-item"
              >
                <span class="requirement-bullet">•</span>
                <span class="requirement-text">{{ requirement }}</span>
              </div>
            </div>
          </div>

          <!-- 用户协议 -->
          <div class="form-section">
            <Checkbox
              v-model:checked="formData.agreeToTerms"
              class="agreement-checkbox"
              :disabled="configLoading"
            >
              我已阅读并同意
              <a @click="viewUserAgreement" class="agreement-link">
                《{{ cloneSetConfig?.protocol_name || '用户协议' }}》
              </a>
            </Checkbox>
          </div>

          <!-- 克隆按钮 -->
          <div class="form-section">
            <Button
              type="primary"
              size="large"
              :loading="loading"
              :disabled="!canSubmit"
              class="submit-btn"
              @click="handleSubmit"
            >
              {{ consumePoints }}点/1次
            </Button>
          </div>
        </div>
      </Card>
    </div>
  </Page>
</template>

<style lang="scss" scoped>
@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .ai-voice-clone-container {
    max-width: 100%;
  }

  .form-content {
    gap: 24px;
  }

  .clone-mode-group {
    :deep(.ant-radio-wrapper) {
      padding: 10px 12px;
    }
  }

  .audio-uploader {
    :deep(.ant-upload-drag) {
      padding: 24px 16px;
    }
  }

  .upload-icon,
  .progress-icon,
  .success-icon {
    width: 40px;
    height: 40px;
  }

  .upload-title,
  .progress-title,
  .success-title {
    font-size: 14px;
  }

  .upload-subtitle,
  .progress-subtitle,
  .success-subtitle {
    font-size: 12px;
  }
}

.ai-voice-clone-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
  max-width: 800px;
  margin: 0 auto;
}

.form-card {
  background: hsl(var(--card));
  border: 1px solid hsl(var(--border));
  border-radius: 12px;
  box-shadow: 0 4px 12px hsl(var(--shadow) / 8%);
}

.back-button {
  display: flex;
  gap: 8px;
  align-items: center;
  padding: 8px 12px;
  font-size: 16px;
  font-weight: 600;
  color: hsl(var(--foreground));
  transition: all 0.3s ease;

  &:hover {
    color: hsl(var(--primary));
    background: hsl(var(--primary) / 5%);
  }

  .back-icon {
    width: 20px;
    height: 20px;
  }
}

.form-content {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.form-section {
  display: flex;
  flex-direction: column;
  gap: 16px;

  .section-title {
    margin-bottom: 8px;
    font-size: 16px;
    font-weight: 600;
    color: hsl(var(--foreground));

    .format-hint {
      font-size: 14px;
      font-weight: 400;
      color: hsl(var(--muted-foreground));
    }
  }
}

.clone-mode-group {
  display: flex;
  flex-direction: column;
  gap: 12px;

  :deep(.ant-radio-wrapper) {
    padding: 12px 16px;
    background: hsl(var(--background));
    border: 1px solid hsl(var(--border));
    border-radius: 8px;
    transition: all 0.3s ease;

    &:hover {
      background: hsl(var(--accent));
      border-color: hsl(var(--primary) / 50%);
    }

    &.ant-radio-wrapper-checked {
      background: hsl(var(--primary) / 5%);
      border-color: hsl(var(--primary));
    }
  }
}

.clone-title-input {
  :deep(.ant-input) {
    padding: 12px 16px;
    font-size: 14px;
    color: hsl(var(--foreground));
    background: hsl(var(--background));
    border-radius: 8px;
    transition: all 0.3s ease;

    &:hover {
      border-color: hsl(var(--primary) / 50%);
    }

    &:focus {
      border-color: hsl(var(--primary));
      box-shadow: 0 0 0 2px hsl(var(--primary) / 20%);
    }

    &::placeholder {
      color: hsl(var(--muted-foreground));
    }
  }

  :deep(.ant-input-suffix) {
    font-size: 12px;
    color: hsl(var(--muted-foreground));
  }
}

.audio-uploader {
  :deep(.ant-upload-drag) {
    padding: 32px 24px;
    background: hsl(var(--background));
    border: 2px dashed hsl(var(--border));
    border-radius: 12px;
    transition: all 0.3s ease;

    &:hover {
      background: hsl(var(--accent));
      border-color: hsl(var(--primary) / 50%);
    }

    &.ant-upload-drag-hover {
      border-color: hsl(var(--primary));
    }
  }
}

.upload-placeholder,
.upload-progress,
.upload-success,
.upload-error {
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: center;
  text-align: center;
}

.upload-icon,
.progress-icon,
.success-icon,
.error-icon {
  width: 48px;
  height: 48px;
  color: hsl(var(--muted-foreground));

  svg {
    width: 100%;
    height: 100%;
  }
}

.progress-icon {
  color: hsl(var(--primary));

  .loading-icon {
    animation: spin 1s linear infinite;
  }
}

.success-icon {
  color: hsl(var(--primary));
}

.error-icon {
  color: hsl(var(--destructive));
}

.upload-text,
.progress-text,
.success-text,
.error-text {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.upload-title,
.progress-title,
.success-title,
.error-title {
  font-size: 16px;
  font-weight: 500;
  color: hsl(var(--foreground));
}

.upload-subtitle,
.progress-subtitle,
.success-subtitle,
.error-subtitle {
  font-size: 14px;
  color: hsl(var(--muted-foreground));

  .upload-status {
    padding: 2px 6px;
    margin-left: 8px;
    font-size: 12px;
    color: hsl(var(--primary));
    background: hsl(var(--primary) / 10%);
    border-radius: 4px;
  }
}

.error-subtitle {
  color: hsl(var(--destructive));
}

.upload-success,
.upload-error {
  position: relative;

  .remove-btn {
    position: absolute;
    top: -8px;
    right: -8px;
  }
}

.requirements-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 16px;
  background: hsl(var(--muted) / 30%);
  border: 1px solid hsl(var(--border));
  border-radius: 8px;

  .requirement-item {
    display: flex;
    gap: 8px;
    align-items: flex-start;
    font-size: 14px;
    line-height: 1.5;

    .requirement-bullet {
      flex-shrink: 0;
      margin-top: 2px;
      font-weight: bold;
      color: hsl(var(--primary));
    }

    .requirement-text {
      color: hsl(var(--foreground));
    }
  }
}

/* 用户协议样式 */

.agreement-checkbox {
  font-size: 14px;

  .agreement-link {
    color: hsl(var(--primary));
    text-decoration: none;
    cursor: pointer;

    &:hover {
      text-decoration: underline;
    }
  }

  :deep(.ant-checkbox-wrapper) {
    font-size: 14px;
    color: hsl(var(--foreground));

    .ant-checkbox {
      .ant-checkbox-inner {
        background: hsl(var(--background));
        border-color: hsl(var(--border));
      }

      &.ant-checkbox-checked .ant-checkbox-inner {
        background: hsl(var(--primary));
        border-color: hsl(var(--primary));
      }
    }
  }
}

.submit-btn {
  width: 100%;
  height: 48px;
  font-size: 16px;
  font-weight: 600;
  background: hsl(var(--primary));
  border-color: hsl(var(--primary));
  border-radius: 8px;

  &:hover:not(:disabled) {
    background: hsl(var(--primary) / 90%);
    border-color: hsl(var(--primary) / 90%);
  }

  &:disabled {
    color: hsl(var(--muted-foreground));
    background: hsl(var(--muted));
    border-color: hsl(var(--muted));
  }
}
</style>
