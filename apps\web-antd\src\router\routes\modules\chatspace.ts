import type { RouteRecordRaw } from 'vue-router';

import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'lucide:layout-chatspace',
      order: -1,
      title: $t('page.chatspace.title'),
      hideInMenu: true,
    },
    name: 'chatspace',
    path: '/Chatspace',
    children: [
      {
        name: 'Bot-0',
        path: '/bot-0',
        component: () => import('#/views/chatspace/bot-0/index.vue'),
        meta: {
          icon: 'lucide:area-chart',
          title: $t('page.chatspace.bot-0'),
        },
      },
    ],
  },
];

export default routes;
